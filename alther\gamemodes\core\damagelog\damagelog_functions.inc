#include <YSI_Coding\y_hooks>

// OPTIMIZED: Batch damage logs and reduce database load
static damageLogQueue[MAX_PLAYERS][10][256]; // Queue for batch processing
static damageLogCount[MAX_PLAYERS];

hook OnPlayerTakeDamage(playerid, issuerid, Float:amount, weaponid, bodypart)
{
    if(issuerid != INVALID_PLAYER_ID && issuerid != playerid)
    {
        if(!AccountData[issuerid][pInEvent] && !AccountData[playerid][pInEvent])
        {
            // Only log significant damage (>= 5.0) to reduce spam
            if(amount >= 5.0)
            {
                new weaponName[35];
                GetWeaponName(weaponid, weaponName, sizeof(weaponName));

                // Add to queue instead of immediate database insert
                format(damageLogQueue[playerid][damageLogCount[playerid]], 256,
                    "(%d, '%e', '%e', '%.2f', '%e', '%e', '%e')",
                    AccountData[playerid][pID], GetSimpleTime(), weaponName, amount,
                    GetBodyPartName(bodypart), AccountData[issuerid][pName], AccountData[playerid][pName]);

                damageLogCount[playerid]++;

                // Batch insert when queue is full or player disconnects
                if(damageLogCount[playerid] >= 10)
                {
                    FlushDamageLogQueue(playerid);
                }
            }
        }
    }
    else
    {
        if(!AccountData[playerid][pInEvent])
        {
            new sjhtr[512], weaponName[35];
            GetWeaponName(weaponid, weaponName, sizeof (weaponName));
            mysql_format(g_SQL, sjhtr, sizeof(sjhtr), "INSERT INTO `damagelogs` SET `OwnerID` = %d, `Date` = '%e', `Weapon` = '%e', `Damage` = '%.2f', `BodyPart` = '%e', `IssuerName` = 'Unknown', `KorbanName` = '%e'", 
            AccountData[playerid][pID], GetSimpleTime(), weaponName, amount, GetBodyPartName(bodypart), AccountData[playerid][pName]);
            mysql_pquery(g_SQL, sjhtr);
        }
    }
    return 1;
}

// Function to flush damage log queue (batch insert)
FlushDamageLogQueue(playerid)
{
    if(damageLogCount[playerid] > 0)
    {
        new query[2048] = "INSERT INTO `damagelogs` (`OwnerID`, `Date`, `Weapon`, `Damage`, `BodyPart`, `IssuerName`, `KorbanName`) VALUES ";

        for(new i = 0; i < damageLogCount[playerid]; i++)
        {
            strcat(query, damageLogQueue[playerid][i]);
            if(i < damageLogCount[playerid] - 1) strcat(query, ", ");
        }

        mysql_pquery(g_SQL, query);
        damageLogCount[playerid] = 0;
    }
    return 1;
}

// Hook to flush queue on disconnect
hook OnPlayerDisconnect(playerid, reason)
{
    FlushDamageLogQueue(playerid);
    return 1;
}