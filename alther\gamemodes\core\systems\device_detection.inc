// ========================================
// DEVICE DETECTION SYSTEM
// ========================================
// Sistem untuk mendeteksi device player (Android/PC)

#include <YSI_Coding\y_hooks>

// Device detection variables
enum E_DEVICE_TYPE
{
    DEVICE_UNKNOWN,
    DEVICE_PC,
    DEVICE_ANDROID
}

static playerDeviceType[MAX_PLAYERS];
static bool:deviceDetected[MAX_PLAYERS];
static deviceCheckTimer[MAX_PLAYERS];

// Enhanced device detection functions
stock GetPlayerDeviceType(playerid)
{
    if(!IsPlayerConnected(playerid)) return DEVICE_UNKNOWN;
    return playerDeviceType[playerid];
}

stock bool:IsPlayerAndroid(playerid)
{
    return (GetPlayerDeviceType(playerid) == DEVICE_ANDROID);
}

stock bool:IsPlayerPC(playerid)
{
    return (GetPlayerDeviceType(playerid) == DEVICE_PC);
}

stock const GetPlayerDeviceString(playerid)
{
    switch(GetPlayerDeviceType(playerid))
    {
        case DEVICE_ANDROID: return "Android";
        case DEVICE_PC: return "PC";
        default: return "Unknown";
    }
}

stock const GetPlayerDeviceColorString(playerid)
{
    switch(GetPlayerDeviceType(playerid))
    {
        case DEVICE_ANDROID: return ""ORANGE"Android";
        case DEVICE_PC: return ""LIGHTBLUE"PC";
        default: return ""GRAY"Unknown";
    }
}

// Enhanced detection using multiple methods
DetectPlayerDevice(playerid)
{
    if(!IsPlayerConnected(playerid)) return 0;
    
    // Method 1: YSI Android detection (if available)
    #if defined IsAndroidPlayer
    if(IsAndroidPlayer(playerid))
    {
        playerDeviceType[playerid] = DEVICE_ANDROID;
        deviceDetected[playerid] = true;
        printf("[DEVICE] Player %s detected as Android (YSI)", AccountData[playerid][pName]);
        return 1;
    }
    else if(IsPCPlayer(playerid))
    {
        playerDeviceType[playerid] = DEVICE_PC;
        deviceDetected[playerid] = true;
        printf("[DEVICE] Player %s detected as PC (YSI)", AccountData[playerid][pName]);
        return 1;
    }
    #endif
    
    // Method 2: Client version analysis
    new version[24];
    GetPlayerVersion(playerid, version, sizeof(version));
    
    // Android clients often have specific version patterns
    if(strfind(version, "0.3.7", true) != -1)
    {
        // Additional checks for Android indicators
        new ping = GetPlayerPing(playerid);
        new Float:packetLoss = GetPlayerPacketLoss(playerid);
        
        // Android devices typically have higher ping and packet loss
        if(ping > 150 || packetLoss > 2.0)
        {
            playerDeviceType[playerid] = DEVICE_ANDROID;
            deviceDetected[playerid] = true;
            printf("[DEVICE] Player %s detected as Android (heuristic: ping=%d, loss=%.2f)", 
                AccountData[playerid][pName], ping, packetLoss);
            return 1;
        }
    }
    
    // Method 3: Behavioral analysis
    // Android players often have different input patterns
    static connectTime[MAX_PLAYERS];
    if(connectTime[playerid] == 0)
    {
        connectTime[playerid] = gettime();
    }
    
    // If still unknown after 30 seconds, assume PC
    if(gettime() - connectTime[playerid] > 30 && !deviceDetected[playerid])
    {
        playerDeviceType[playerid] = DEVICE_PC;
        deviceDetected[playerid] = true;
        printf("[DEVICE] Player %s assumed as PC (timeout)", AccountData[playerid][pName]);
        return 1;
    }
    
    return 0;
}

// Timer untuk device detection
forward DeviceDetectionTimer(playerid);
public DeviceDetectionTimer(playerid)
{
    if(!IsPlayerConnected(playerid) || deviceDetected[playerid])
    {
        if(deviceCheckTimer[playerid] != 0)
        {
            KillTimer(deviceCheckTimer[playerid]);
            deviceCheckTimer[playerid] = 0;
        }
        return 0;
    }
    
    DetectPlayerDevice(playerid);
    return 1;
}

// Alternative detection using client check response
#if defined OnClientCheckResponse
forward DeviceDetection_OnClientCheckResponse(playerid, actionid, memaddr, retndata);
public OnClientCheckResponse(playerid, actionid, memaddr, retndata)
{
    // Android detection based on client response patterns
    if(actionid == 0x48) // Standard PC response
    {
        if(!deviceDetected[playerid])
        {
            playerDeviceType[playerid] = DEVICE_PC;
            deviceDetected[playerid] = true;
            printf("[DEVICE] Player %s detected as PC (client response)", AccountData[playerid][pName]);
        }
    }
    
    #if defined DeviceDetection_OnClientCheckResponse
        return DeviceDetection_OnClientCheckResponse(playerid, actionid, memaddr, retndata);
    #else
        return 1;
    #endif
}
#if defined _ALS_OnClientCheckResponse
    #undef OnClientCheckResponse
#else
    #define _ALS_OnClientCheckResponse
#endif
#define OnClientCheckResponse DeviceDetection_OnClientCheckResponse
#endif

// Hook untuk inisialisasi
hook OnPlayerConnect(playerid)
{
    playerDeviceType[playerid] = DEVICE_UNKNOWN;
    deviceDetected[playerid] = false;
    
    // Start detection timer
    deviceCheckTimer[playerid] = SetTimerEx("DeviceDetectionTimer", 2000, true, "i", playerid);
    
    return 1;
}

hook OnPlayerDisconnect(playerid, reason)
{
    if(deviceCheckTimer[playerid] != 0)
    {
        KillTimer(deviceCheckTimer[playerid]);
        deviceCheckTimer[playerid] = 0;
    }
    
    playerDeviceType[playerid] = DEVICE_UNKNOWN;
    deviceDetected[playerid] = false;
    
    return 1;
}

// Hook untuk spawn - final detection attempt
hook OnPlayerSpawn(playerid)
{
    if(!deviceDetected[playerid])
    {
        // Force detection on spawn
        SetTimerEx("DeviceDetectionTimer", 1000, false, "i", playerid);
    }
    return 1;
}

// Admin command untuk check device
YCMD:checkdevice(playerid, params[], help)
{
    if(AccountData[playerid][pAdmin] < 1)
        return PermissionError(playerid);
    
    new targetid;
    if(sscanf(params, "d", targetid))
        return SUM(playerid, "/checkdevice [playerid]");
    
    if(!IsPlayerConnected(targetid))
        return ShowTDN(playerid, NOTIFICATION_ERROR, "Player tidak terkoneksi!");
    
    new deviceInfo[128];
    format(deviceInfo, sizeof(deviceInfo), 
        "Player %s (ID: %d) menggunakan device: %s", 
        AccountData[targetid][pName], targetid, GetPlayerDeviceString(targetid));
    
    SendClientMessage(playerid, Y_WHITE, deviceInfo);
    
    // Additional info
    new version[24];
    GetPlayerVersion(targetid, version, sizeof(version));
    new ping = GetPlayerPing(targetid);
    new Float:packetLoss = GetPlayerPacketLoss(targetid);
    
    format(deviceInfo, sizeof(deviceInfo), 
        "Version: %s | Ping: %d | Packet Loss: %.2f%%", 
        version, ping, packetLoss);
    
    SendClientMessage(playerid, Y_GRAY, deviceInfo);
    
    return 1;
}

// Function untuk update nametag dengan device info
UpdatePlayerNametagWithDevice(adminid, targetid)
{
    if(!IsPlayerConnected(adminid) || !IsPlayerConnected(targetid))
        return 0;
    
    if(!AccountData[adminid][pToggleNameID])
        return 0;
    
    // Destroy existing label
    if(IsValidDynamic3DTextLabel(AccountData[adminid][pNameIDLabel][targetid]))
    {
        DestroyDynamic3DTextLabel(AccountData[adminid][pNameIDLabel][targetid]);
    }
    
    // Create new label with device info
    new labelText[200];
    format(labelText, sizeof(labelText), "%s [%d]\n%s\n"RED"%.1f "WHITE"| "GRAY"%.1f", 
        AccountData[targetid][pName], targetid, GetPlayerDeviceColorString(targetid),
        AccountData[targetid][pHealth], AccountData[targetid][pArmor]);
    
    AccountData[adminid][pNameIDLabel][targetid] = CreateDynamic3DTextLabel(
        labelText, Y_WHITE, 0.0, 0.0, 0.6, 100.5, targetid, 
        INVALID_VEHICLE_ID, 0, -1, -1, adminid, 100.5, -1, 0);
    
    return 1;
}

// Task untuk update device detection berkala
task DeviceDetectionUpdate[30000]() // Every 30 seconds
{
    new detectedCount = 0, unknownCount = 0;
    
    foreach(new playerid : Player)
    {
        if(deviceDetected[playerid])
        {
            detectedCount++;
        }
        else
        {
            unknownCount++;
            // Try detection again
            DetectPlayerDevice(playerid);
        }
    }
    
    if(unknownCount > 0)
    {
        printf("[DEVICE] Detection status: %d detected, %d unknown", detectedCount, unknownCount);
    }
    
    return 1;
}

// Statistics function
GetDeviceStatistics(&androidCount, &pcCount, &unknownCount)
{
    androidCount = 0;
    pcCount = 0;
    unknownCount = 0;
    
    foreach(new playerid : Player)
    {
        switch(GetPlayerDeviceType(playerid))
        {
            case DEVICE_ANDROID: androidCount++;
            case DEVICE_PC: pcCount++;
            default: unknownCount++;
        }
    }
    return 1;
}

// Admin command untuk device statistics
YCMD:devicestats(playerid, params[], help)
{
    if(AccountData[playerid][pAdmin] < 1)
        return PermissionError(playerid);
    
    new androidCount, pcCount, unknownCount;
    GetDeviceStatistics(androidCount, pcCount, unknownCount);
    
    new statsMsg[128];
    format(statsMsg, sizeof(statsMsg), 
        "Device Statistics: Android: %d | PC: %d | Unknown: %d", 
        androidCount, pcCount, unknownCount);
    
    SendClientMessage(playerid, Y_WHITE, statsMsg);
    return 1;
}
